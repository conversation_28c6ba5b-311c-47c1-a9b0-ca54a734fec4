#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NMEA数据解析脚本
提取GNGGA或GPGGA语句中的UTC时间和经纬度信息
"""

import re
import sys
from datetime import datetime
from typing import List, Tuple, Optional


class NMEAParser:
    """NMEA数据解析器"""
    
    def __init__(self):
        # GNGGA和GPGGA语句的正则表达式
        self.gga_pattern = re.compile(r'^(\$G[PN]GGA),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*),([^,]*)\*([0-9A-F]{2})$')
    
    def parse_coordinate(self, coord_str: str, direction: str) -> Optional[float]:
        """
        解析NMEA坐标格式为十进制度数
        
        Args:
            coord_str: 坐标字符串 (如: 3958.123456)
            direction: 方向 (N/S/E/W)
            
        Returns:
            十进制度数，如果解析失败返回None
        """
        if not coord_str or not direction:
            return None
            
        try:
            # NMEA格式: DDMM.MMMMMM (度分格式)
            if len(coord_str) < 4:
                return None
                
            # 分离度和分
            if '.' in coord_str:
                dot_pos = coord_str.index('.')
                if dot_pos < 2:
                    return None
                degrees = int(coord_str[:dot_pos-2])
                minutes = float(coord_str[dot_pos-2:])
            else:
                degrees = int(coord_str[:-2])
                minutes = float(coord_str[-2:])
            
            # 转换为十进制度数
            decimal_degrees = degrees + minutes / 60.0
            
            # 根据方向调整符号
            if direction in ['S', 'W']:
                decimal_degrees = -decimal_degrees
                
            return decimal_degrees
            
        except (ValueError, IndexError):
            return None
    
    def parse_time(self, time_str: str) -> Optional[str]:
        """
        解析NMEA时间格式
        
        Args:
            time_str: 时间字符串 (HHMMSS.SSS)
            
        Returns:
            格式化的时间字符串，如果解析失败返回None
        """
        if not time_str or len(time_str) < 6:
            return None
            
        try:
            # 提取时分秒
            hours = int(time_str[:2])
            minutes = int(time_str[2:4])
            seconds = float(time_str[4:])
            
            # 验证时间有效性
            if hours > 23 or minutes > 59 or seconds >= 60:
                return None
                
            return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}"
            
        except (ValueError, IndexError):
            return None
    
    def parse_gga_line(self, line: str) -> Optional[dict]:
        """
        解析单行GGA数据
        
        Args:
            line: NMEA语句行
            
        Returns:
            解析结果字典，包含时间、经纬度等信息
        """
        line = line.strip()
        match = self.gga_pattern.match(line)
        
        if not match:
            return None
            
        groups = match.groups()
        
        # 提取各字段
        sentence_type = groups[0]  # $GNGGA 或 $GPGGA
        utc_time = groups[1]      # UTC时间
        latitude = groups[2]      # 纬度
        lat_dir = groups[3]       # 纬度方向 (N/S)
        longitude = groups[4]     # 经度
        lon_dir = groups[5]       # 经度方向 (E/W)
        quality = groups[6]       # 定位质量
        num_sats = groups[7]      # 卫星数量
        hdop = groups[8]          # 水平精度因子
        altitude = groups[9]      # 海拔高度
        alt_unit = groups[10]     # 海拔单位
        geoid_height = groups[11] # 大地水准面高度
        geoid_unit = groups[12]   # 大地水准面高度单位
        dgps_time = groups[13]    # DGPS时间
        checksum = groups[14]     # 校验和
        
        # 解析时间
        parsed_time = self.parse_time(utc_time)
        if not parsed_time:
            return None
            
        # 解析坐标
        lat_decimal = self.parse_coordinate(latitude, lat_dir)
        lon_decimal = self.parse_coordinate(longitude, lon_dir)
        
        if lat_decimal is None or lon_decimal is None:
            return None
            
        return {
            'sentence_type': sentence_type,
            'utc_time': parsed_time,
            'latitude': lat_decimal,
            'longitude': lon_decimal,
            'quality': quality,
            'num_satellites': num_sats,
            'hdop': hdop,
            'altitude': altitude,
            'raw_line': line
        }
    
    def parse_file(self, filename: str) -> List[dict]:
        """
        解析NMEA文件
        
        Args:
            filename: 文件路径
            
        Returns:
            解析结果列表
        """
        results = []
        
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    if 'GGA' in line and ('$GNGGA' in line or '$GPGGA' in line):
                        parsed = self.parse_gga_line(line)
                        if parsed:
                            parsed['line_number'] = line_num
                            results.append(parsed)
                        else:
                            print(f"警告: 第{line_num}行解析失败: {line.strip()}")
                            
        except FileNotFoundError:
            print(f"错误: 文件 '{filename}' 不存在")
        except Exception as e:
            print(f"错误: 读取文件时发生异常: {e}")
            
        return results
    
    def save_to_csv(self, results: List[dict], output_file: str):
        """
        将结果保存为CSV文件
        
        Args:
            results: 解析结果列表
            output_file: 输出文件路径
        """
        import csv
        
        if not results:
            print("没有数据可保存")
            return
            
        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['line_number', 'sentence_type', 'utc_time', 'latitude', 'longitude', 
                             'quality', 'num_satellites', 'hdop', 'altitude']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in results:
                    # 只写入需要的字段
                    row = {k: result.get(k, '') for k in fieldnames}
                    writer.writerow(row)
                    
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"错误: 保存CSV文件时发生异常: {e}")


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python nmea_parser.py <NMEA文件路径> [输出CSV文件路径]")
        print("示例: python nmea_parser.py data.nmea output.csv")
        return
        
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建解析器
    parser = NMEAParser()
    
    # 解析文件
    print(f"正在解析文件: {input_file}")
    results = parser.parse_file(input_file)
    
    if not results:
        print("未找到有效的GNGGA或GPGGA数据")
        return
        
    print(f"成功解析 {len(results)} 条记录")
    
    # 显示前几条记录
    print("\n前5条记录:")
    print("-" * 80)
    for i, result in enumerate(results[:5]):
        print(f"记录 {i+1}:")
        print(f"  行号: {result['line_number']}")
        print(f"  类型: {result['sentence_type']}")
        print(f"  UTC时间: {result['utc_time']}")
        print(f"  纬度: {result['latitude']:.6f}°")
        print(f"  经度: {result['longitude']:.6f}°")
        print(f"  定位质量: {result['quality']}")
        print(f"  卫星数: {result['num_satellites']}")
        print()
    
    # 保存到CSV文件
    if output_file:
        parser.save_to_csv(results, output_file)
    else:
        # 自动生成输出文件名
        import os
        base_name = os.path.splitext(input_file)[0]
        output_file = f"{base_name}_parsed.csv"
        parser.save_to_csv(results, output_file)


if __name__ == "__main__":
    main()
